# HSQL Database Configuration
spring.datasource.driver-class-name=org.hsqldb.jdbc.JDBCDriver
spring.datasource.url=****************************************
spring.datasource.username=sa
spring.datasource.password=

# Spring Configuration
spring.main.allow-bean-definition-overriding=true
spring.main.allow-circular-references=true
skipTestInitialization=false

# Logging Configuration
logging.level.com.nscorp=WARN
logging.level.org.springframework=ERROR
logging.level.org.hsqldb=WARN
logging.level.com.zaxxer.hikari=ERROR
logging.level.root=WARN

# Enable to see sql statements.
# logging.level.org.springframework.jdbc.core=WARN
# logging.level.org.springframework.WARN
# logging.level.root=WARN

# Test-specific settings
TestUtils.assertionsEnabled=true
flatTransactions=true
testTransactions=true

