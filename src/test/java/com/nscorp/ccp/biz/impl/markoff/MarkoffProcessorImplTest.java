//package com.nscorp.ccp.biz.impl.markoff;
//
//import com.nscorp.ccp.biz.markoff.MarkoffProcessor;
//import com.nscorp.ccp.common.markoff.MarkoffStudyStatus;
//import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
//import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
//import com.nscorp.ccp.common.markoff.MarkoffStudy;
//import com.nscorp.ccp.common.markoff.MarkoffData;
//import com.nscorp.ccp.testing.util.SpringBasedTest;
//import com.nscorp.ccp.testing.util.TestInitializer;
//import lombok.extern.slf4j.Slf4j;
//import lombok.val;
//import org.junit.jupiter.api.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.PostConstruct;
//import java.time.Instant;
//import java.time.LocalDate;
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//
//@Tag("slow")
//@Slf4j
//@DisplayName("MarkoffProcessorImpl Integration Tests")
//class MarkoffProcessorImplTest extends SpringBasedTest {
//
//    @Autowired
//    private MarkoffProcessor markoffProcessor;
//
//    @Autowired
//    private MarkoffInputDataDAO markoffInputDataDAO;
//
//    @Autowired
//    private MarkoffAndStudyDataDAO markoffAndStudyDataDAO;
//
//    @Autowired
//    private TestInitializer testInitializer;
//
//    @PostConstruct
//    public void init() {
//        testInitializer.initTests();
//    }
//
//    @Test
//    @DisplayName("Should process markoff data successfully with valid parameters")
//    @Transactional
//    void shouldProcessMarkoffDataSuccessfully() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then - Should not throw any exceptions
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data for startDate: {}, studyLength: {}", startDate, studyLengthMonths);
//        });
//    }
//
//    @Test
//    @DisplayName("Should handle different study lengths")
//    @Transactional
//    void shouldHandleDifferentStudyLengths() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengths = new Integer[]{1, 3, 6, 12, 24};
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then - Test each study length
//        for (val studyLength : studyLengths) {
//            assertDoesNotThrow(() -> {
//                markoffProcessor.processMarkoffData(startDate, studyLength, markoffDataId, markoffStudyId);
//                log.info("Successfully processed markoff data with study length: {} months", studyLength);
//            });
//        }
//    }
//
//    @Test
//    @DisplayName("Should handle different start dates")
//    @Transactional
//    void shouldHandleDifferentStartDates() {
//        // Given
//        val startDates = new LocalDate[]{
//            LocalDate.of(2023, 1, 1),
//            LocalDate.of(2023, 6, 15),
//            LocalDate.of(2023, 12, 31),
//            LocalDate.of(2024, 3, 1)
//        };
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then - Test each start date
//        for (val startDate : startDates) {
//            assertDoesNotThrow(() -> {
//                markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//                log.info("Successfully processed markoff data with start date: {}", startDate);
//            });
//        }
//    }
//
//    @Test
//    @DisplayName("Should verify MarkoffInputDataDAO integration")
//    @Transactional
//    void shouldVerifyMarkoffInputDataDAOIntegration() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 6;
//
//        // When & Then - Test DAO methods directly
//        assertDoesNotThrow(() -> {
//            val inputData = markoffInputDataDAO.getMarkoffInputData(startDate, studyLengthMonths);
//            assertNotNull(inputData);
//            log.info("Successfully retrieved markoff input data");
//
//            val poolData = markoffInputDataDAO.getMarkoffPoolData();
//            assertNotNull(poolData);
//            log.info("Successfully retrieved markoff pool data");
//
//            val inputDataByDates = markoffInputDataDAO.getMarkoffInputDataByDates(
//                startDate.minusMonths(6), startDate, studyLengthMonths);
//            assertNotNull(inputDataByDates);
//            log.info("Successfully retrieved markoff input data by dates");
//        });
//    }
//
//    @Test
//    @DisplayName("Should verify MarkoffAndStudyDataDAO integration")
//    @Transactional
//    void shouldVerifyMarkoffAndStudyDataDAOIntegration() {
//        // Given
//        val testStudy = createTestStudy("Integration test study");
//
//        // When & Then - Test DAO methods
//        assertDoesNotThrow(() -> {
//            // Test save study
//            val savedStudy = markoffAndStudyDataDAO.save(testStudy);
//            assertNotNull(savedStudy);
//            log.info("Successfully saved study: {}", savedStudy.getDescription());
//
//            // Test get study by ID
//            if (savedStudy.getMarkoffStudyId() != null) {
//                val retrievedStudy = markoffAndStudyDataDAO.getStudyById(savedStudy.getMarkoffStudyId());
//                log.info("Successfully retrieved study by ID: {}", savedStudy.getMarkoffStudyId());
//            }
//
//            // Test get all studies
//            val allStudies = markoffAndStudyDataDAO.getAllStudies();
//            assertNotNull(allStudies);
//            log.info("Successfully retrieved all studies");
//
//            // Test get latest successful study
//            val latestStudy = markoffAndStudyDataDAO.getLatestSuccessfulStudy();
//            log.info("Successfully retrieved latest successful study");
//        });
//    }
//
//    @Test
//    @DisplayName("Should handle complete workflow integration")
//    @Transactional
//    void shouldHandleCompleteWorkflowIntegration() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then - Test complete workflow
//        assertDoesNotThrow(() -> {
//            // Step 1: Verify input data retrieval
//            val inputData = markoffInputDataDAO.getMarkoffInputData(startDate, studyLengthMonths);
//            assertNotNull(inputData);
//
//            // Step 2: Verify pool data retrieval
//            val poolData = markoffInputDataDAO.getMarkoffPoolData();
//            assertNotNull(poolData);
//
//            // Step 3: Process the data through the processor
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//
//            log.info("Complete workflow integration test completed successfully");
//        });
//    }
//
//    @Test
//    @DisplayName("Should handle edge case with minimum study length")
//    @Transactional
//    void shouldHandleEdgeCaseWithMinimumStudyLength() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 1; // Minimum study length
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data with minimum study length: {} month", studyLengthMonths);
//        });
//    }
//
//    @Test
//    @DisplayName("Should handle edge case with maximum study length")
//    @Transactional
//    void shouldHandleEdgeCaseWithMaximumStudyLength() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 36; // Large study length
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data with maximum study length: {} months", studyLengthMonths);
//        });
//    }
//
//    @Test
//    @DisplayName("Should verify processor with different markoff data IDs")
//    @Transactional
//    void shouldVerifyProcessorWithDifferentMarkoffDataIds() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 6;
//        val markoffDataIds = new Long[]{1L, 100L, 999L, 1000L};
//        val markoffStudyId = 1L;
//
//        // When & Then - Test with different markoff data IDs
//        for (val markoffDataId : markoffDataIds) {
//            assertDoesNotThrow(() -> {
//                markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//                log.info("Successfully processed markoff data with ID: {}", markoffDataId);
//            });
//        }
//    }
//
//    @Test
//    @DisplayName("Should verify processor with different study IDs")
//    @Transactional
//    void shouldVerifyProcessorWithDifferentStudyIds() {
//        // Given
//        val startDate = LocalDate.of(2023, 6, 1);
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val studyIds = new Long[]{1L, 50L, 100L, 500L};
//
//        // When & Then - Test with different study IDs
//        for (val studyId : studyIds) {
//            assertDoesNotThrow(() -> {
//                markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, studyId);
//                log.info("Successfully processed markoff data with study ID: {}", studyId);
//            });
//        }
//    }
//
//    @Test
//    @DisplayName("Should test DAO saveAll functionality")
//    @Transactional
//    void shouldTestDAOSaveAllFunctionality() {
//        // Given
//        val testMarkoffDataList = createTestMarkoffDataList(1L);
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            val savedData = markoffAndStudyDataDAO.saveAll(testMarkoffDataList);
//            assertNotNull(savedData);
//            log.info("Successfully saved markoff data list using saveAll");
//        });
//    }
//
//    @Test
//    @DisplayName("Should test DAO getMarkoffDataByStudyId functionality")
//    @Transactional
//    void shouldTestDAOGetMarkoffDataByStudyIdFunctionality() {
//        // Given
//        val studyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            val markoffData = markoffAndStudyDataDAO.getMarkoffDataByStudyId(studyId);
//            assertNotNull(markoffData);
//            log.info("Successfully retrieved markoff data by study ID: {}", studyId);
//        });
//    }
//
//    @Test
//    @DisplayName("Should verify processor handles current date")
//    @Transactional
//    void shouldVerifyProcessorHandlesCurrentDate() {
//        // Given
//        val startDate = LocalDate.now();
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data with current date: {}", startDate);
//        });
//    }
//
//    @Test
//    @DisplayName("Should verify processor handles past dates")
//    @Transactional
//    void shouldVerifyProcessorHandlesPastDates() {
//        // Given
//        val startDate = LocalDate.of(2020, 1, 1); // Past date
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data with past date: {}", startDate);
//        });
//    }
//
//    @Test
//    @DisplayName("Should verify processor handles future dates")
//    @Transactional
//    void shouldVerifyProcessorHandlesFutureDates() {
//        // Given
//        val startDate = LocalDate.of(2025, 12, 31); // Future date
//        val studyLengthMonths = 6;
//        val markoffDataId = 1L;
//        val markoffStudyId = 1L;
//
//        // When & Then
//        assertDoesNotThrow(() -> {
//            markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
//            log.info("Successfully processed markoff data with future date: {}", startDate);
//        });
//    }
//
//    @Nested
//    @DisplayName("DAO Integration Tests")
//    class DAOIntegrationTests {
//
//        @Test
//        @DisplayName("Should verify MarkoffInputDataDAO getMarkoffInputDataByDates with various date ranges")
//        @Transactional
//        void shouldVerifyGetMarkoffInputDataByDatesWithVariousDateRanges() {
//            // Given
//            val dateRanges = new LocalDate[][]{
//                {LocalDate.of(2023, 1, 1), LocalDate.of(2023, 6, 30)},
//                {LocalDate.of(2023, 3, 1), LocalDate.of(2023, 9, 30)},
//                {LocalDate.of(2022, 12, 1), LocalDate.of(2023, 5, 31)}
//            };
//            val studyLengthMonths = 6;
//
//            // When & Then
//            for (val dateRange : dateRanges) {
//                val startDatePast = dateRange[0];
//                val endDateFuture = dateRange[1];
//
//                assertDoesNotThrow(() -> {
//                    val inputData = markoffInputDataDAO.getMarkoffInputDataByDates(
//                        startDatePast, endDateFuture, studyLengthMonths);
//                    assertNotNull(inputData);
//                    log.info("Successfully retrieved input data for date range: {} to {}",
//                        startDatePast, endDateFuture);
//                });
//            }
//        }
//
//        @Test
//        @DisplayName("Should verify MarkoffAndStudyDataDAO with multiple studies")
//        @Transactional
//        void shouldVerifyMarkoffAndStudyDataDAOWithMultipleStudies() {
//            // Given
//            val studies = new MarkoffStudy[]{
//                createTestStudy("First integration study"),
//                createTestStudy("Second integration study"),
//                createTestStudy("Third integration study")
//            };
//
//            // When & Then
//            val savedStudies = new ArrayList<MarkoffStudy>();
//            for (val study : studies) {
//                assertDoesNotThrow(() -> {
//                    val savedStudy = markoffAndStudyDataDAO.save(study);
//                    assertNotNull(savedStudy);
//                    savedStudies.add(savedStudy);
//                    log.info("Successfully saved study: {}", savedStudy.getDescription());
//                });
//            }
//
//            // Verify retrieval of all studies
//            assertDoesNotThrow(() -> {
//                val allStudies = markoffAndStudyDataDAO.getAllStudies();
//                assertNotNull(allStudies);
//                log.info("Successfully retrieved all studies");
//            });
//        }
//
//        @Test
//        @DisplayName("Should verify complete DAO workflow")
//        @Transactional
//        void shouldVerifyCompleteDAOWorkflow() {
//            // Given
//            val study = createTestStudy("Complete workflow study");
//
//            // When & Then - Test complete DAO workflow
//            assertDoesNotThrow(() -> {
//                // Step 1: Save study
//                val savedStudy = markoffAndStudyDataDAO.save(study);
//                assertNotNull(savedStudy);
//
//                // Step 2: Create and save markoff data
//                val markoffDataList = createTestMarkoffDataList(savedStudy.getMarkoffStudyId());
//                val savedMarkoffData = markoffAndStudyDataDAO.saveAll(markoffDataList);
//                assertNotNull(savedMarkoffData);
//
//                // Step 3: Retrieve markoff data by study ID
//                if (savedStudy.getMarkoffStudyId() != null) {
//                    val retrievedData = markoffAndStudyDataDAO.getMarkoffDataByStudyId(
//                        savedStudy.getMarkoffStudyId());
//                    assertNotNull(retrievedData);
//                }
//
//                log.info("Complete DAO workflow test completed successfully");
//            });
//        }
//    }
//
//    @Nested
//    @DisplayName("Processor Logic Integration Tests")
//    class ProcessorLogicIntegrationTests {
//
//        @Test
//        @DisplayName("Should verify processor with various parameter combinations")
//        @Transactional
//        void shouldVerifyProcessorWithVariousParameterCombinations() {
//            // Given
//            val parameterCombinations = new Object[][]{
//                {LocalDate.of(2023, 1, 1), 3, 1L, 1L},
//                {LocalDate.of(2023, 6, 15), 6, 2L, 2L},
//                {LocalDate.of(2023, 12, 31), 12, 3L, 3L},
//                {LocalDate.of(2024, 3, 1), 24, 4L, 4L}
//            };
//
//            // When & Then
//            for (val params : parameterCombinations) {
//                val startDate = (LocalDate) params[0];
//                val studyLength = (Integer) params[1];
//                val dataId = (Long) params[2];
//                val studyId = (Long) params[3];
//
//                assertDoesNotThrow(() -> {
//                    markoffProcessor.processMarkoffData(startDate, studyLength, dataId, studyId);
//                    log.info("Successfully processed with params: date={}, length={}, dataId={}, studyId={}",
//                        startDate, studyLength, dataId, studyId);
//                });
//            }
//        }
//
//        @Test
//        @DisplayName("Should verify processor integration with both DAOs")
//        @Transactional
//        void shouldVerifyProcessorIntegrationWithBothDAOs() {
//            // Given
//            val startDate = LocalDate.of(2023, 6, 1);
//            val studyLengthMonths = 6;
//
//            // When & Then - Verify processor uses both DAOs
//            assertDoesNotThrow(() -> {
//                // This will internally call both DAO methods
//                markoffProcessor.processMarkoffData(startDate, studyLengthMonths, 1L, 1L);
//
//                // Verify DAOs are accessible independently
//                val inputData = markoffInputDataDAO.getMarkoffInputData(startDate, studyLengthMonths);
//                assertNotNull(inputData);
//
//                val poolData = markoffInputDataDAO.getMarkoffPoolData();
//                assertNotNull(poolData);
//
//                val testStudy = createTestStudy("DAO integration test");
//                val savedStudy = markoffAndStudyDataDAO.save(testStudy);
//                assertNotNull(savedStudy);
//
//                log.info("Successfully verified processor integration with both DAOs");
//            });
//        }
//    }
//
//    // Helper methods
//    private MarkoffStudy createTestStudy(String description) {
//        return MarkoffStudy.builder()
//            .runTs(Instant.now())
//            .startDate(LocalDate.of(2023, 1, 1))
//            .endDate(LocalDate.of(2023, 6, 30))
//            .status(MarkoffStudyStatus.COMPLETED)
//            .description(description)
//            .creationUser("test_user")
//            .errorMessage("")
//            .build();
//    }
//
//    private List<MarkoffData> createTestMarkoffDataList(Long studyId) {
//        val markoffDataList = new ArrayList<MarkoffData>();
//
//        for (int i = 1; i <= 3; i++) {
//            val markoffData = MarkoffData.builder()
//                .markoffStudyId(studyId)
//                .distr("D" + i)
//                .subDistr("S" + i)
//                .poolName("Pool" + i)
//                .craft("Craft" + i)
//                .distinctCntEmp((long) (i * 10))
//                .empTotalStateDaysMedian((double) (i * 100))
//                .totalStateDays((double) (i * 1000))
//                .stateDays((double) (i * 500))
//                .rate(0.5 * i)
//                .empTotalStateDaysAvg((double) (i * 150))
//                .build();
//            markoffDataList.add(markoffData);
//        }
//
//        return markoffDataList;
//    }
//}