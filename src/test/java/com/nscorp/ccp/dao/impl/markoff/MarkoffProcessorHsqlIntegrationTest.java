package com.nscorp.ccp.dao.impl.markoff;

import com.nscorp.ccp.TestConfig;
import com.nscorp.ccp.biz.markoff.MarkoffProcessor;
import com.nscorp.ccp.config.HsqlIntegrationTestConfiguration;
import com.nscorp.ccp.dao.markoff.MarkoffAndStudyDataDAO;
import com.nscorp.ccp.dao.markoff.MarkoffInputDataDAO;
import com.nscorp.ccp.testing.util.SpringBasedTest;
import com.nscorp.ccp.testing.util.TestInitializer;
import com.nscorp.ccp.testing.util.TestUtils;
import com.nscorp.ccp.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for MarkoffProcessor using HSQL database.
 * This test class demonstrates how to write integration tests that use real DAO implementations
 * with an in-memory HSQL database instead of mocked DAOs.
 */
@Tag("slow")
@Slf4j
@DisplayName("MarkoffProcessor HSQL Integration Tests")
@Import({HsqlIntegrationTestConfiguration.class})
class MarkoffProcessorHsqlIntegrationTest extends SpringBasedTest {

    @Autowired
    private MarkoffProcessor markoffProcessor;

    @Autowired
    private MarkoffInputDataDAO markoffInputDataDAO;

    @Autowired
    private MarkoffAndStudyDataDAO markoffAndStudyDataDAO;

    @Autowired
    private TestInitializer testInitializer;

    @PostConstruct
    public void init() {
        testInitializer.initTests();
    }

    @Test
    @DisplayName("Should test MarkoffInputDataDAO with HSQL database")
    @Transactional
    void test_MarkoffInputDataDAO_Integration() {
        TestUtils.runTestAndAssert("MarkoffInputDataDAO_Integration", () -> {
            // Given
            val startDate = LocalDate.of(2023, 6, 1);
            val studyLengthMonths = 6;

            // When
            val inputData = markoffInputDataDAO.getMarkoffInputData(startDate, studyLengthMonths);
            val poolData = markoffInputDataDAO.getMarkoffPoolData();

            // Then
            assertNotNull(inputData);
            assertNotNull(poolData);
            
            log.info("Successfully retrieved markoff input data and pool data from HSQL database");
            
            return JsonUtils.prettyPrint(new TestResult(
                "MarkoffInputDataDAO integration test completed",
                inputData != null,
                poolData != null
            ));
        });
    }

    @Test
    @DisplayName("Should test MarkoffAndStudyDataDAO with HSQL database")
    @Transactional
    void test_MarkoffAndStudyDataDAO_Integration() {
        TestUtils.runTestAndAssert("MarkoffAndStudyDataDAO_Integration", () -> {
            // When
            val allStudies = markoffAndStudyDataDAO.getAllStudies();
            val latestStudy = markoffAndStudyDataDAO.getLatestSuccessfulStudy();

            // Then
            assertNotNull(allStudies);
            // latestStudy might be null if no completed studies exist in test data
            
            log.info("Successfully retrieved studies from HSQL database");
            
            return JsonUtils.prettyPrint(new TestResult(
                "MarkoffAndStudyDataDAO integration test completed",
                allStudies != null,
                true // Always pass this condition
            ));
        });
    }

    @Test
    @DisplayName("Should test MarkoffProcessor with real DAO implementations and HSQL database")
    @Transactional
    void test_MarkoffProcessor_Integration() {
        TestUtils.runTestAndAssert("MarkoffProcessor_Integration", () -> {
            // Given
            val startDate = LocalDate.of(2023, 6, 1);
            val studyLengthMonths = 6;
            val markoffDataId = 1L;
            val markoffStudyId = 1L;

            // When & Then - Test that the processor can run without throwing exceptions
            assertDoesNotThrow(() -> {
                markoffProcessor.processMarkoffData(startDate, studyLengthMonths, markoffDataId, markoffStudyId);
                log.info("Successfully processed markoff data with HSQL database integration");
            });

            return JsonUtils.prettyPrint(new TestResult(
                "MarkoffProcessor integration test completed successfully",
                true,
                true
            ));
        });
    }

    @Test
    @DisplayName("Should verify database connectivity and test data")
    @Transactional
    void test_Database_Connectivity() {
        TestUtils.runTestAndAssert("Database_Connectivity", () -> {
            // Test that we can retrieve test data that was inserted during database initialization
            val allStudies = markoffAndStudyDataDAO.getAllStudies();
            
            assertNotNull(allStudies);
            
            // Count the studies
            val studyCount = com.google.common.collect.Iterables.size(allStudies);
            log.info("Found {} studies in test database", studyCount);
            
            // Verify we have at least some test data
            assertTrue(studyCount > 0, "Should have test data in the database");
            
            return JsonUtils.prettyPrint(new TestResult(
                "Database connectivity test completed",
                studyCount > 0,
                allStudies != null
            ));
        });
    }

    /**
     * Simple test result class for JSON serialization
     */
    private static class TestResult {
        public final String message;
        public final boolean condition1;
        public final boolean condition2;

        public TestResult(String message, boolean condition1, boolean condition2) {
            this.message = message;
            this.condition1 = condition1;
            this.condition2 = condition2;
        }
    }
}
