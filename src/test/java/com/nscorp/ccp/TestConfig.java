package com.nscorp.ccp;

import com.nscorp.ccp.testing.util.LogOutputInitializer;
import com.nscorp.ccp.utils.date.DateTimeProvider;
import com.nscorp.ccp.utils.db.*;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;

@TestConfiguration
@Slf4j
@RequiredArgsConstructor
public class TestConfig {

	@Value("${spring.datasource.url}")
	private String jdbcUrl;

	@Value("${spring.datasource.username}")
	private String username;

	@Value("${spring.datasource.password}")
	private String password;

	@Value("${spring.datasource.driver-class-name}")
	private String driverClassName;

	@Value("${skipTestInitialization:false}")
	private boolean skipTestInitialization;

	private static boolean dbInitialized = false;

	@Bean("scpmDataSource")
	@Primary
	public DataSource scpmDataSource(){
		return buildDs(true);
	}

	@Bean("dataWarehouseDataSource")
	public DataSource dataWarehouseDataSource(){
		return buildDs(true);
	}

	@Bean
	public JdbcTemplate jdbcTemplate(DataSource scpmDataSource) {
		return new JdbcTemplate(scpmDataSource);
	}

	@Bean
	public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource scpmDataSource) {
		return new NamedParameterJdbcTemplate(scpmDataSource);
	}

	private synchronized DataSource buildDs(boolean setSchema) {
		if ( ! skipTestInitialization ) {
			LogOutputInitializer.init();
			if ( ! dbInitialized ) {
				val dbInitializer = new DbInitializer(new DbPopulator(), new DataImporter());
				dbInitializer.initdb(jdbcUrl, username, password, driverClassName);
				dbInitialized = true;
			}
		}
		final HikariDataSource result = new HikariDataSource();
		result.setJdbcUrl(jdbcUrl);
		result.setUsername(username);
		result.setPassword(password);
		result.setDriverClassName(driverClassName);
		result.setMaximumPoolSize(5);
		result.setMinimumIdle(1);
		if ( setSchema ) {
			result.setSchema("CCP");
			result.setConnectionInitSql("set schema CCP");
		}
		return result;
	}
}
