package com.nscorp.ccp.logic.markoff;

import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import one.util.streamex.StreamEx;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

@Tag("fast")
@Slf4j
class MarkoffCalculatorTest {
	@Test
	void testStreamex() {
		val list = ImmutableList.of(1,2,3,4,5);
		val result = StreamEx.of(list).
				pairMap((el1,el2)->String.format("[el1=%d, el2=%d]", el1, el2)).
				mapLast(last->String.format("*LAST* %s", last)).
				toImmutableList();
		log.warn(result.toString());



	}
}
